import 'package:flutter_test/flutter_test.dart';
import 'package:livelook/models/user_model.dart';
import 'package:livelook/models/post_model.dart';
import 'package:livelook/models/like_model.dart';
import 'package:livelook/models/comment_model.dart';

void main() {
  group('Model Tests', () {
    test('UserModel should serialize and deserialize correctly', () {
      final user = UserModel(
        userId: 'test_user_id',
        name: 'Test User',
        email: '<EMAIL>',
        joinTime: DateTime.now(),
      );

      final map = user.toMap();
      final deserializedUser = UserModel.fromMap(map);

      expect(deserializedUser.userId, equals(user.userId));
      expect(deserializedUser.name, equals(user.name));
      expect(deserializedUser.email, equals(user.email));
    });

    test('PostModel should serialize and deserialize correctly', () {
      final post = PostModel(
        postId: 'test_post_id',
        postTitle: 'Test Post',
        userId: 'test_user_id',
        likeAt: 10,
        commentAt: 5,
        saveAt: 3,
        createdAt: DateTime.now(),
      );

      final map = post.toMap();
      final deserializedPost = PostModel.fromMap(map);

      expect(deserializedPost.postId, equals(post.postId));
      expect(deserializedPost.postTitle, equals(post.postTitle));
      expect(deserializedPost.likeAt, equals(post.likeAt));
      expect(deserializedPost.commentAt, equals(post.commentAt));
      expect(deserializedPost.saveAt, equals(post.saveAt));
    });

    test('LikeModel should serialize and deserialize correctly', () {
      final like = LikeModel(
        likeId: 'test_like_id',
        postId: 'test_post_id',
        postTitle: 'Test Post',
        userId: 'test_user_id',
        likeAt: DateTime.now().millisecondsSinceEpoch,
      );

      final map = like.toMap();
      final deserializedLike = LikeModel.fromMap(map);

      expect(deserializedLike.likeId, equals(like.likeId));
      expect(deserializedLike.postId, equals(like.postId));
      expect(deserializedLike.userId, equals(like.userId));
      expect(deserializedLike.likeAt, equals(like.likeAt));
    });

    test('CommentModel should serialize and deserialize correctly', () {
      final comment = CommentModel(
        commentId: 'test_comment_id',
        comment: 'This is a test comment',
        userId: 'test_user_id',
        commentAt: DateTime.now().millisecondsSinceEpoch,
      );

      final map = comment.toMap();
      final deserializedComment = CommentModel.fromMap(map);

      expect(deserializedComment.commentId, equals(comment.commentId));
      expect(deserializedComment.comment, equals(comment.comment));
      expect(deserializedComment.userId, equals(comment.userId));
      expect(deserializedComment.commentAt, equals(comment.commentAt));
    });
  });
}
