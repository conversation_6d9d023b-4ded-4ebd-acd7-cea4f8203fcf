import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/auth_service.dart';
import '../models/user_model.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();

  User? _user;
  UserModel? _userModel;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  User? get user => _user;
  UserModel? get userModel => _userModel;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;

  AuthProvider() {
    // Listen to auth state changes
    _authService.authStateChanges.listen((User? user) async {
      _user = user;
      if (user != null) {
        await _loadUserData(user.uid);
      } else {
        _userModel = null;
      }
      notifyListeners();
    });
  }

  // Load user data from Firestore
  Future<void> _loadUserData(String userId) async {
    try {
      _userModel = await _authService.getUserData(userId);
    } catch (e) {
      // Error loading user data: $e
      debugPrint('Error loading user data: $e');
    }
  }

  // Sign in with Google
  Future<bool> signInWithGoogle() async {
    try {
      _setLoading(true);
      _clearError();

      final userCredential = await _authService.signInWithGoogle();

      if (userCredential != null) {
        _user = userCredential.user;
        if (_user != null) {
          await _loadUserData(_user!.uid);
        }
        _setLoading(false);
        return true;
      } else {
        _setError('Sign in was cancelled');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to sign in: $e');
      _setLoading(false);
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      _setLoading(true);
      await _authService.signOut();
      _user = null;
      _userModel = null;
      _setLoading(false);
    } catch (e) {
      _setError('Failed to sign out: $e');
      _setLoading(false);
    }
  }

  // Update user profile
  Future<bool> updateProfile({String? name, String? profileImageUrl}) async {
    if (_user == null) return false;

    try {
      _setLoading(true);

      final success = await _authService.updateUserProfile(
        userId: _user!.uid,
        name: name,
        profileImageUrl: profileImageUrl,
      );

      if (success) {
        await _loadUserData(_user!.uid);
      }

      _setLoading(false);
      return success;
    } catch (e) {
      _setError('Failed to update profile: $e');
      _setLoading(false);
      return false;
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
