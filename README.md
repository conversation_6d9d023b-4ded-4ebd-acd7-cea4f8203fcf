# ETERNAL NOTIFY - Community Data Sharing App

A Flutter community data sharing app with Google authentication, featuring a modern UI with post interactions, user profiles, and real-time data synchronization using Firebase. Stay connected and informed with real-time notifications.

## Features

### 🔐 Authentication
- **Google Sign-In**: Secure authentication using Google OAuth
- **User Profile Management**: Edit profile information and view user details
- **Automatic User Creation**: New users are automatically added to Firestore

### 🏠 Home Feed
- **Real-time Post Feed**: View all community posts in real-time
- **Post Interactions**: Like, comment, and save posts
- **Interactive UI**: Modern card-based design with smooth animations

### 📱 Navigation
- **Custom Bottom Navigation**: 3-tab navigation with emphasized home button
- **Saved Posts**: Dedicated tab for viewing saved posts
- **Profile Management**: Complete profile section with settings

### 🎨 UI/UX
- **Modern Design**: Clean, Material Design 3 interface
- **Custom App Bar**: Logo and notification icon
- **Responsive Layout**: Works on web and mobile platforms
- **Loading States**: Proper loading indicators and error handling

## Tech Stack

- **Frontend**: Flutter (Dart)
- **Backend**: Firebase (Firestore, Authentication)
- **Authentication**: Google Sign-In
- **State Management**: Provider pattern
- **Database**: Cloud Firestore
- **Platform**: Web, Android, iOS

## Project Structure

```
lib/
├── models/           # Data models
│   ├── user_model.dart
│   ├── post_model.dart
│   ├── like_model.dart
│   └── comment_model.dart
├── providers/        # State management
│   └── auth_provider.dart
├── services/         # Business logic
│   ├── auth_service.dart
│   └── firestore_service.dart
├── screens/          # UI screens
│   ├── auth/
│   │   └── login_screen.dart
│   ├── home/
│   │   └── home_screen.dart
│   ├── saved/
│   │   └── saved_posts_screen.dart
│   ├── profile/
│   │   └── profile_screen.dart
│   └── main_screen.dart
├── widgets/          # Reusable components
│   └── post_card.dart
├── firebase_options.dart
└── main.dart
```

## Firebase Collections

### Users Collection
```json
{
  "user_id": "string",
  "Name": "string",
  "gmail": "string",
  "profile_image_url": "string",
  "join_time": "timestamp"
}
```

### Posts Collection
```json
{
  "post_id": "string",
  "post_title": "string",
  "user_id": "string",
  "like_at": "number",
  "coment_at": "number",
  "save_at": "number",
  "saved_by": ["array of user_ids"],
  "created_at": "timestamp"
}
```

### Likes Collection
```json
{
  "like_id": "string",
  "post_id": "string",
  "post_title": "string",
  "user_id": "string",
  "like_at": "number"
}
```

### Comments Collection
```json
{
  "comment_id": "string",
  "comment": "string",
  "user_id": "string",
  "comment_at": "number"
}
```

## Getting Started

### Prerequisites
- Flutter SDK (3.8.0 or higher)
- Firebase project with Firestore and Authentication enabled
- Google Cloud Console project for OAuth

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd livelook
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Setup**
   - Create a Firebase project
   - Enable Firestore Database
   - Enable Authentication with Google provider
   - Download `google-services.json` for Android
   - Update `firebase_options.dart` with your project configuration

4. **Google Sign-In Setup**
   - Configure OAuth consent screen in Google Cloud Console
   - Add your domain to authorized domains
   - Update `web/index.html` with your web client ID

5. **Run the app**
   ```bash
   # For web
   flutter run -d chrome

   # For Android
   flutter run -d android

   # For iOS
   flutter run -d ios
   ```

## Configuration

### Firebase Configuration
Update `lib/firebase_options.dart` with your Firebase project details:
- Project ID
- API Keys
- App IDs
- Storage bucket

### Google Sign-In Web Configuration
Update `web/index.html` with your web client ID:
```html
<meta name="google-signin-client_id" content="YOUR_WEB_CLIENT_ID">
```

## Features in Detail

### Authentication Flow
1. User opens app and sees login screen
2. Clicks "Continue with Google"
3. Google OAuth flow completes
4. User data is saved to Firestore
5. User is redirected to main app

### Post Interactions
- **Like**: Toggle like status and update counters
- **Comment**: Add comments to posts
- **Save**: Save posts to personal collection
- **Real-time Updates**: All changes sync across devices

### Profile Management
- View user information
- Edit profile name
- Sign out functionality
- View join date

## Testing

Run the test suite:
```bash
flutter test
```

The project includes model tests to verify data serialization and deserialization.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please open an issue in the repository.
