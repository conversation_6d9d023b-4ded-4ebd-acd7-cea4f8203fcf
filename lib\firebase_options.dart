// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCh0LJmBrs_yhpPzG_zlB5ZXEKNHgtOKrY',
    appId: '1:1021500035212:web:livelookweb',
    messagingSenderId: '1021500035212',
    projectId: 'lookapp-9645e',
    authDomain: 'lookapp-9645e.firebaseapp.com',
    storageBucket: 'lookapp-9645e.firebasestorage.app',
    measurementId: 'G-MEASUREMENT_ID',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCh0LJmBrs_yhpPzG_zlB5ZXEKNHgtOKrY',
    appId: '1:1021500035212:android:5991c8f85f1758b034eba9',
    messagingSenderId: '1021500035212',
    projectId: 'lookapp-9645e',
    storageBucket: 'lookapp-9645e.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCh0LJmBrs_yhpPzG_zlB5ZXEKNHgtOKrY',
    appId: '1:1021500035212:ios:5991c8f85f1758b034eba9',
    messagingSenderId: '1021500035212',
    projectId: 'lookapp-9645e',
    storageBucket: 'lookapp-9645e.firebasestorage.app',
    iosBundleId: 'com.example.livelook',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCh0LJmBrs_yhpPzG_zlB5ZXEKNHgtOKrY',
    appId: '1:1021500035212:ios:5991c8f85f1758b034eba9',
    messagingSenderId: '1021500035212',
    projectId: 'lookapp-9645e',
    storageBucket: 'lookapp-9645e.firebasestorage.app',
    iosBundleId: 'com.example.livelook',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCh0LJmBrs_yhpPzG_zlB5ZXEKNHgtOKrY',
    appId: '1:1021500035212:web:5991c8f85f1758b034eba9',
    messagingSenderId: '1021500035212',
    projectId: 'lookapp-9645e',
    authDomain: 'lookapp-9645e.firebaseapp.com',
    storageBucket: 'lookapp-9645e.firebasestorage.app',
  );
}
