class CommentModel {
  final String commentId;
  final String comment;
  final String userId;
  final int commentAt;

  CommentModel({
    required this.commentId,
    required this.comment,
    required this.userId,
    required this.commentAt,
  });

  factory CommentModel.fromMap(Map<String, dynamic> map) {
    return CommentModel(
      commentId: map['comment_id'] ?? '',
      comment: map['comment'] ?? '',
      userId: map['user_id'] ?? '',
      commentAt: map['comment_at'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'comment_id': commentId,
      'comment': comment,
      'user_id': userId,
      'comment_at': commentAt,
    };
  }

  CommentModel copyWith({
    String? commentId,
    String? comment,
    String? userId,
    int? commentAt,
  }) {
    return CommentModel(
      commentId: commentId ?? this.commentId,
      comment: comment ?? this.comment,
      userId: userId ?? this.userId,
      commentAt: commentAt ?? this.commentAt,
    );
  }
}
