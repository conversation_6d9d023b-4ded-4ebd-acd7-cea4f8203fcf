class UserModel {
  final String userId;
  final String name;
  final String email;
  final String? profileImageUrl;
  final DateTime joinTime;
  final String? password;

  UserModel({
    required this.userId,
    required this.name,
    required this.email,
    this.profileImageUrl,
    required this.joinTime,
    this.password,
  });

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      userId: map['user_id'] ?? '',
      name: map['Name'] ?? '',
      email: map['gmail'] ?? '',
      profileImageUrl: map['profile_image_url'],
      joinTime: map['join_time']?.toDate() ?? DateTime.now(),
      password: map['password'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'user_id': userId,
      'Name': name,
      'gmail': email,
      'profile_image_url': profileImageUrl,
      'join_time': joinTime,
      'password': password,
    };
  }

  UserModel copyWith({
    String? userId,
    String? name,
    String? email,
    String? profileImageUrl,
    DateTime? joinTime,
    String? password,
  }) {
    return UserModel(
      userId: userId ?? this.userId,
      name: name ?? this.name,
      email: email ?? this.email,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      joinTime: joinTime ?? this.joinTime,
      password: password ?? this.password,
    );
  }
}
