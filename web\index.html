<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="LiveLook - Community Data Sharing App">

  <!-- Google Sign-In -->
  <meta name="google-signin-client_id" content="1021500035212-6r3bk0s12pvov65vdovdttnc05nukj4u.apps.googleusercontent.com">

  <!-- Firebase Configuration -->
  <script type="module">
    // Import the functions you need from the SDKs you need
    import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.0/firebase-app.js";
    import { getAuth } from "https://www.gstatic.com/firebasejs/10.7.0/firebase-auth.js";
    import { getFirestore } from "https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore.js";

    // Your web app's Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyCh0LJmBrs_yhpPzG_zlB5ZXEKNHgtOKrY",
      authDomain: "lookapp-9645e.firebaseapp.com",
      projectId: "lookapp-9645e",
      storageBucket: "lookapp-9645e.firebasestorage.app",
      messagingSenderId: "1021500035212",
      appId: "1:1021500035212:web:livelookweb"
    };

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const auth = getAuth(app);
    const db = getFirestore(app);

    // Make Firebase available globally
    window.firebase = { app, auth, db };
  </script>

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="livelook">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>ETERNAL NOTIFY</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
