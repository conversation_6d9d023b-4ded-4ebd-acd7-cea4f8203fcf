class PostModel {
  final String postId;
  final String postTitle;
  final String userId;
  final int likeAt;
  final int commentAt;
  final int saveAt;
  final DateTime createdAt;

  PostModel({
    required this.postId,
    required this.postTitle,
    required this.userId,
    required this.likeAt,
    required this.commentAt,
    required this.saveAt,
    required this.createdAt,
  });

  factory PostModel.fromMap(Map<String, dynamic> map) {
    return PostModel(
      postId: map['post_id'] ?? '',
      postTitle: map['post_title'] ?? '',
      userId: map['user_id'] ?? '',
      likeAt: map['like_at'] ?? 0,
      commentAt: map['coment_at'] ?? 0, // Note: typo in Firestore field name
      saveAt: map['save_at'] ?? 0,
      createdAt: map['created_at']?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'post_id': postId,
      'post_title': postTitle,
      'user_id': userId,
      'like_at': likeAt,
      'coment_at': commentAt, // Note: typo in Firestore field name
      'save_at': saveAt,
      'created_at': createdAt,
    };
  }

  PostModel copyWith({
    String? postId,
    String? postTitle,
    String? userId,
    int? likeAt,
    int? commentAt,
    int? saveAt,
    DateTime? createdAt,
  }) {
    return PostModel(
      postId: postId ?? this.postId,
      postTitle: postTitle ?? this.postTitle,
      userId: userId ?? this.userId,
      likeAt: likeAt ?? this.likeAt,
      commentAt: commentAt ?? this.commentAt,
      saveAt: saveAt ?? this.saveAt,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
