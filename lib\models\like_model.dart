class LikeModel {
  final String likeId;
  final String postId;
  final String postTitle;
  final String userId;
  final int likeAt;

  LikeModel({
    required this.likeId,
    required this.postId,
    required this.postTitle,
    required this.userId,
    required this.likeAt,
  });

  factory LikeModel.fromMap(Map<String, dynamic> map) {
    return LikeModel(
      likeId: map['like_id'] ?? '',
      postId: map['post_id'] ?? '',
      postTitle: map['post_title'] ?? '',
      userId: map['user_id'] ?? '',
      likeAt: map['like_at'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'like_id': likeId,
      'post_id': postId,
      'post_title': postTitle,
      'user_id': userId,
      'like_at': likeAt,
    };
  }

  LikeModel copyWith({
    String? likeId,
    String? postId,
    String? postTitle,
    String? userId,
    int? likeAt,
  }) {
    return LikeModel(
      likeId: likeId ?? this.likeId,
      postId: postId ?? this.postId,
      postTitle: postTitle ?? this.postTitle,
      userId: userId ?? this.userId,
      likeAt: likeAt ?? this.likeAt,
    );
  }
}
