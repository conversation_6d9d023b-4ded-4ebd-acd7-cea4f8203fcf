import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/post_model.dart';
import '../models/like_model.dart';
import '../models/comment_model.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get all posts stream
  Stream<List<PostModel>> getPostsStream() {
    return _firestore
        .collection('post')
        .orderBy('created_at', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) => PostModel.fromMap({...doc.data(), 'post_id': doc.id}),
              )
              .toList(),
        );
  }

  // Get user's saved posts
  Stream<List<PostModel>> getSavedPostsStream(String userId) {
    return _firestore
        .collection('post')
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .where((doc) {
                    final data = doc.data();
                    final savedBy = List<String>.from(data['saved_by'] ?? []);
                    return savedBy.contains(userId);
                  })
                  .map(
                    (doc) =>
                        PostModel.fromMap({...doc.data(), 'post_id': doc.id}),
                  )
                  .toList()
                ..sort((a, b) => b.createdAt.compareTo(a.createdAt)),
        );
  }

  // Like a post
  Future<bool> likePost(String postId, String userId, String postTitle) async {
    try {
      final batch = _firestore.batch();

      // Check if user already liked this post
      final existingLike = await _firestore
          .collection('like')
          .where('post_id', isEqualTo: postId)
          .where('user_id', isEqualTo: userId)
          .get();

      if (existingLike.docs.isNotEmpty) {
        // Unlike the post
        batch.delete(existingLike.docs.first.reference);

        // Decrease like count
        final postRef = _firestore.collection('post').doc(postId);
        batch.update(postRef, {'like_at': FieldValue.increment(-1)});
      } else {
        // Like the post
        final likeRef = _firestore.collection('like').doc();
        final likeModel = LikeModel(
          likeId: likeRef.id,
          postId: postId,
          postTitle: postTitle,
          userId: userId,
          likeAt: DateTime.now().millisecondsSinceEpoch,
        );

        batch.set(likeRef, likeModel.toMap());

        // Increase like count
        final postRef = _firestore.collection('post').doc(postId);
        batch.update(postRef, {'like_at': FieldValue.increment(1)});
      }

      await batch.commit();
      return true;
    } catch (e) {
      print('Error liking post: $e');
      return false;
    }
  }

  // Save/unsave a post
  Future<bool> toggleSavePost(String postId, String userId) async {
    try {
      final postRef = _firestore.collection('post').doc(postId);
      final postDoc = await postRef.get();

      if (!postDoc.exists) return false;

      final data = postDoc.data()!;
      List<String> savedBy = List<String>.from(data['saved_by'] ?? []);

      if (savedBy.contains(userId)) {
        // Unsave the post
        savedBy.remove(userId);
        await postRef.update({
          'saved_by': savedBy,
          'save_at': FieldValue.increment(-1),
        });
      } else {
        // Save the post
        savedBy.add(userId);
        await postRef.update({
          'saved_by': savedBy,
          'save_at': FieldValue.increment(1),
        });
      }

      return true;
    } catch (e) {
      print('Error toggling save post: $e');
      return false;
    }
  }

  // Add a comment
  Future<bool> addComment(String postId, String userId, String comment) async {
    try {
      final batch = _firestore.batch();

      // Add comment to comments collection
      final commentRef = _firestore.collection('comment').doc();
      final commentModel = CommentModel(
        commentId: commentRef.id,
        comment: comment,
        userId: userId,
        commentAt: DateTime.now().millisecondsSinceEpoch,
      );

      batch.set(commentRef, commentModel.toMap());

      // Increase comment count in post
      final postRef = _firestore.collection('post').doc(postId);
      batch.update(postRef, {'coment_at': FieldValue.increment(1)});

      await batch.commit();
      return true;
    } catch (e) {
      print('Error adding comment: $e');
      return false;
    }
  }

  // Get comments for a post
  Stream<List<CommentModel>> getCommentsStream(String postId) {
    return _firestore
        .collection('comment')
        .where('post_id', isEqualTo: postId)
        .orderBy('comment_at', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map(
                (doc) =>
                    CommentModel.fromMap({...doc.data(), 'comment_id': doc.id}),
              )
              .toList(),
        );
  }

  // Check if user liked a post
  Future<bool> hasUserLikedPost(String postId, String userId) async {
    try {
      final result = await _firestore
          .collection('like')
          .where('post_id', isEqualTo: postId)
          .where('user_id', isEqualTo: userId)
          .get();

      return result.docs.isNotEmpty;
    } catch (e) {
      print('Error checking if user liked post: $e');
      return false;
    }
  }

  // Check if user saved a post
  Future<bool> hasUserSavedPost(String postId, String userId) async {
    try {
      final postDoc = await _firestore.collection('post').doc(postId).get();
      if (!postDoc.exists) return false;

      final data = postDoc.data()!;
      List<String> savedBy = List<String>.from(data['saved_by'] ?? []);

      return savedBy.contains(userId);
    } catch (e) {
      print('Error checking if user saved post: $e');
      return false;
    }
  }
}
