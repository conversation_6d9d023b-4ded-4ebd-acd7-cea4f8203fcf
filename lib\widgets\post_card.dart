import 'package:flutter/material.dart';
import '../models/post_model.dart';
import '../services/firestore_service.dart';

class PostCard extends StatefulWidget {
  final PostModel post;
  final String currentUserId;

  const PostCard({super.key, required this.post, required this.currentUserId});

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  final FirestoreService _firestoreService = FirestoreService();
  bool _isLiked = false;
  bool _isSaved = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkUserInteractions();
  }

  Future<void> _checkUserInteractions() async {
    final liked = await _firestoreService.hasUserLikedPost(
      widget.post.postId,
      widget.currentUserId,
    );
    final saved = await _firestoreService.hasUserSavedPost(
      widget.post.postId,
      widget.currentUserId,
    );

    if (mounted) {
      setState(() {
        _isLiked = liked;
        _isSaved = saved;
      });
    }
  }

  Future<void> _toggleLike() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    final success = await _firestoreService.likePost(
      widget.post.postId,
      widget.currentUserId,
      widget.post.postTitle,
    );

    if (success && mounted) {
      setState(() {
        _isLiked = !_isLiked;
      });
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _toggleSave() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    final success = await _firestoreService.toggleSavePost(
      widget.post.postId,
      widget.currentUserId,
    );

    if (success && mounted) {
      setState(() {
        _isSaved = !_isSaved;
      });
    }

    setState(() {
      _isLoading = false;
    });
  }

  void _showCommentDialog() {
    final TextEditingController commentController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Comment'),
        content: TextField(
          controller: commentController,
          decoration: const InputDecoration(
            hintText: 'Write your comment...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (commentController.text.trim().isNotEmpty) {
                await _firestoreService.addComment(
                  widget.post.postId,
                  widget.currentUserId,
                  commentController.text.trim(),
                );
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Comment added!')),
                  );
                }
              }
            },
            child: const Text('Post'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Post Title
            Text(
              widget.post.postTitle,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),

            const SizedBox(height: 12),

            // Post Stats Row
            Row(
              children: [
                // Likes
                _buildStatItem(
                  icon: Icons.favorite_outline,
                  count: widget.post.likeAt,
                  label: 'Likes',
                  color: Colors.red,
                ),

                const SizedBox(width: 24),

                // Comments
                _buildStatItem(
                  icon: Icons.comment_outlined,
                  count: widget.post.commentAt,
                  label: 'Comments',
                  color: Colors.blue,
                ),

                const SizedBox(width: 24),

                // Saves
                _buildStatItem(
                  icon: Icons.bookmark_outline,
                  count: widget.post.saveAt,
                  label: 'Saves',
                  color: Colors.green,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                // Like Button
                _buildActionButton(
                  icon: _isLiked ? Icons.favorite : Icons.favorite_outline,
                  label: 'Like',
                  color: _isLiked ? Colors.red : Colors.grey.shade600,
                  onTap: _toggleLike,
                ),

                // Comment Button
                _buildActionButton(
                  icon: Icons.comment_outlined,
                  label: 'Comment',
                  color: Colors.grey.shade600,
                  onTap: _showCommentDialog,
                ),

                // Save Button
                _buildActionButton(
                  icon: _isSaved ? Icons.bookmark : Icons.bookmark_outline,
                  label: 'Save',
                  color: _isSaved ? Colors.green : Colors.grey.shade600,
                  onTap: _toggleSave,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          '$count',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade700,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: _isLoading ? null : onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
