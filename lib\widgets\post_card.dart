import 'package:flutter/material.dart';
import '../models/post_model.dart';
import '../services/firestore_service.dart';

class PostCard extends StatefulWidget {
  final PostModel post;
  final String currentUserId;

  const PostCard({super.key, required this.post, required this.currentUserId});

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  final FirestoreService _firestoreService = FirestoreService();
  bool _isLiked = false;
  bool _isSaved = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkUserInteractions();
  }

  Future<void> _checkUserInteractions() async {
    final liked = await _firestoreService.hasUserLikedPost(
      widget.post.postId,
      widget.currentUserId,
    );
    final saved = await _firestoreService.hasUserSavedPost(
      widget.post.postId,
      widget.currentUserId,
    );

    if (mounted) {
      setState(() {
        _isLiked = liked;
        _isSaved = saved;
      });
    }
  }

  Future<void> _toggleLike() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    final success = await _firestoreService.likePost(
      widget.post.postId,
      widget.currentUserId,
      widget.post.postTitle,
    );

    if (success && mounted) {
      setState(() {
        _isLiked = !_isLiked;
      });
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _toggleSave() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    final success = await _firestoreService.toggleSavePost(
      widget.post.postId,
      widget.currentUserId,
    );

    if (success && mounted) {
      setState(() {
        _isSaved = !_isSaved;
      });
    }

    setState(() {
      _isLoading = false;
    });
  }

  void _showCommentDialog() {
    final TextEditingController commentController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Comment'),
        content: TextField(
          controller: commentController,
          decoration: const InputDecoration(
            hintText: 'Write your comment...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (commentController.text.trim().isNotEmpty) {
                await _firestoreService.addComment(
                  widget.post.postId,
                  widget.currentUserId,
                  commentController.text.trim(),
                );
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Comment added!')),
                  );
                }
              }
            },
            child: const Text('Post'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Post Title with Modern Typography
            Text(
              widget.post.postTitle,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: Color(0xFF2D3748),
                height: 1.3,
              ),
            ),

            const SizedBox(height: 16),

            // Post Stats Row with Modern Design
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF8F9FF),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  // Likes
                  Expanded(
                    child: _buildStatItem(
                      icon: Icons.favorite_rounded,
                      count: widget.post.likeAt,
                      label: 'Likes',
                      color: const Color(0xFFFF6B6B),
                    ),
                  ),

                  // Comments
                  Expanded(
                    child: _buildStatItem(
                      icon: Icons.chat_bubble_rounded,
                      count: widget.post.commentAt,
                      label: 'Comments',
                      color: const Color(0xFF4ECDC4),
                    ),
                  ),

                  // Saves
                  Expanded(
                    child: _buildStatItem(
                      icon: Icons.bookmark_rounded,
                      count: widget.post.saveAt,
                      label: 'Saves',
                      color: const Color(0xFF6C63FF),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Action Buttons with Modern Design
            Row(
              children: [
                // Like Button
                Expanded(
                  child: _buildActionButton(
                    icon: _isLiked ? Icons.favorite : Icons.favorite_outline,
                    label: 'Like',
                    color: _isLiked
                        ? const Color(0xFFFF6B6B)
                        : const Color(0xFF6C63FF),
                    backgroundColor: _isLiked
                        ? const Color(0xFFFF6B6B).withValues(alpha: 0.1)
                        : const Color(0xFF6C63FF).withValues(alpha: 0.1),
                    onTap: _toggleLike,
                  ),
                ),

                const SizedBox(width: 12),

                // Comment Button
                Expanded(
                  child: _buildActionButton(
                    icon: Icons.chat_bubble_outline,
                    label: 'Comment',
                    color: const Color(0xFF4ECDC4),
                    backgroundColor: const Color(
                      0xFF4ECDC4,
                    ).withValues(alpha: 0.1),
                    onTap: _showCommentDialog,
                  ),
                ),

                const SizedBox(width: 12),

                // Save Button
                Expanded(
                  child: _buildActionButton(
                    icon: _isSaved ? Icons.bookmark : Icons.bookmark_outline,
                    label: 'Save',
                    color: _isSaved
                        ? const Color(0xFF6C63FF)
                        : const Color(0xFF6C63FF),
                    backgroundColor: _isSaved
                        ? const Color(0xFF6C63FF).withValues(alpha: 0.1)
                        : const Color(0xFF6C63FF).withValues(alpha: 0.05),
                    onTap: _toggleSave,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, size: 24, color: color),
        const SizedBox(height: 8),
        Text(
          '$count',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: Color(0xFF2D3748),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required Color backgroundColor,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: _isLoading ? null : onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
